# MSPM0G3507智能小车代码详解

## 📖 文档说明

本文档是基于TI MSPM0G3507微控制器开发的智能小车项目的详细代码解释和硬件连接说明。文档采用简单易懂的语言，配合丰富的代码示例和图表，帮助初学者理解项目的完整实现逻辑。

**适用人群**：嵌入式开发初学者、单片机爱好者、智能小车项目开发者

**文档版本**：v1.0  
**创建日期**：2024年  
**维护者**：开发团队

---

## 🚗 项目概述

### 项目简介

MSPM0G3507智能小车是一个基于TI最新MSPM0系列微控制器的综合性嵌入式项目。该项目集成了电机控制、传感器检测、显示输出、通信传输等多个功能模块，是学习嵌入式系统开发的优秀实践项目。

### 核心功能特性

- **🔧 双电机精确控制**：支持PWM调速和正反转控制
- **📊 编码器反馈**：实时速度和位置检测
- **👁️ 8路灰度检测**：高精度循线功能
- **📺 OLED显示**：128x64像素信息显示
- **🎯 PID闭环控制**：精确的运动控制算法
- **📡 UART通信**：调试和数据传输
- **⚡ 多任务调度**：高效的任务管理系统

### 技术规格

| 项目 | 规格 |
|------|------|
| **微控制器** | TI MSPM0G3507 |
| **架构** | ARM Cortex-M0+ |
| **主频** | 80MHz |
| **封装** | 64-pin LQFP |
| **Flash** | 128KB |
| **SRAM** | 32KB |
| **工作电压** | 1.62V - 3.6V |

---

## 🏗️ 项目架构设计

### 分层架构概述

本项目采用经典的三层架构设计，确保代码的模块化、可维护性和可扩展性：

```
┌─────────────────────────────────────────┐
│              用户应用层 (User)           │
│        ┌─────────────────────────┐      │
│        │      main.c             │      │
│        │   用户配置和主程序       │      │
│        └─────────────────────────┘      │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│             逻辑控制层 (Logic)           │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐   │
│  │scheduler│ │gray_app │ │user_motor│   │
│  │任务调度 │ │灰度应用 │ │电机控制 │   │
│  └─────────┘ └─────────┘ └─────────┘   │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐   │
│  │user_pid │ │  OLED   │ │   ...   │   │
│  │PID控制  │ │显示控制 │ │         │   │
│  └─────────┘ └─────────┘ └─────────┘   │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│             硬件驱动层 (Driver)          │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐   │
│  │motor_   │ │encoder_ │ │uart_    │   │
│  │driver   │ │driver   │ │driver   │   │
│  └─────────┘ └─────────┘ └─────────┘   │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐   │
│  │  pid    │ │  IIC    │ │bsp_system│   │
│  │         │ │         │ │         │   │
│  └─────────┘ └─────────┘ └─────────┘   │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│           硬件抽象层 (HAL)               │
│        ┌─────────────────────────┐      │
│        │  ti_msp_dl_config.h/c   │      │
│        │    硬件配置和初始化      │      │
│        └─────────────────────────┘      │
└─────────────────────────────────────────┘
```

### 各层职责说明

#### 1. 硬件抽象层 (HAL)
- **文件**：`ti_msp_dl_config.h/c`
- **职责**：统一管理所有硬件资源配置
- **功能**：引脚定义、时钟配置、外设初始化

#### 2. 硬件驱动层 (Driver)
- **目录**：`driver/`
- **职责**：提供硬件操作的基础接口
- **特点**：与具体硬件紧密相关，提供标准化API

#### 3. 逻辑控制层 (Logic)
- **目录**：`logic/`
- **职责**：实现具体的业务逻辑和算法
- **特点**：调用驱动层接口，实现复杂功能

#### 4. 用户应用层 (User)
- **目录**：`user/`
- **职责**：系统初始化和主程序流程
- **特点**：整合各个模块，实现完整功能

---

## 🔌 硬件连接详解

### 微控制器引脚分布

MSPM0G3507采用64引脚LQFP封装，主要功能引脚分布如下：

```
                    MSPM0G3507 (64-pin LQFP)
                           ┌─────────┐
                    PA28 ──┤  1   64 ├── VDD
                     PA1 ──┤  2   63 ├── VSS
                     PA0 ──┤  3   62 ├── PA27(ADC)
                     ... ──┤  4   61 ├── PB9(ENC_RB)
                     ... ──┤  5   60 ├── PB8(ENC_RA)
                    PA31 ──┤  6   59 ├── PB7(ENC_LB)
                     ... ──┤  7   58 ├── PB6(ENC_LA)
                     ... ──┤  8   57 ├── ...
                     ... ──┤  9   56 ├── ...
                     PA5 ──┤ 10   55 ├── ...
                     PA6 ──┤ 11   54 ├── PA24
                     ... ──┤ 12   53 ├── ...
                     ... ──┤ 13   52 ├── PB24
                     ... ──┤ 14   51 ├── ...
                     ... ──┤ 15   50 ├── ...
                           └─────────┘
```

### 完整引脚映射表

#### 电源与时钟系统
| 引脚号 | 引脚名称 | GPIO | 功能描述 | 配置说明 |
|--------|----------|------|----------|----------|
| 10 | PA5 | HFXIN | 外部高频晶振输入 | 连接8MHz晶振 |
| 11 | PA6 | HFXOUT | 外部高频晶振输出 | 连接8MHz晶振 |
| 64 | VDD | - | 主电源正极 | 3.3V供电 |
| 63 | VSS | - | 主电源负极 | 接地 |

#### 电机控制系统
| 引脚号 | 引脚名称 | GPIO | 功能描述 | 定时器配置 |
|--------|----------|------|----------|------------|
| 28 | PB11 | PWM_LEFT | 左电机PWM控制 | TIMG8_CCP1, 20kHz |
| 6 | PA31 | PWM_RIGHT | 右电机PWM控制 | TIMG7_CCP1, 20kHz |
| 18 | PA22 | DIR_LEFT1 | 左电机方向控制1 | 数字输出 |
| 23 | PB24 | DIR_LEFT2 | 左电机方向控制2 | 数字输出 |
| 25 | PA24 | DIR_RIGHT1 | 右电机方向控制1 | 数字输出 |
| 30 | PA26 | DIR_RIGHT2 | 右电机方向控制2 | 数字输出 |

#### 编码器接口系统
| 引脚号 | 引脚名称 | GPIO | 功能描述 | 中断配置 |
|--------|----------|------|----------|----------|
| 58 | PB6 | ENC_LA | 左编码器A相 | 上升沿中断 |
| 59 | PB7 | ENC_LB | 左编码器B相 | 下降沿中断 |
| 60 | PB8 | ENC_RA | 右编码器A相 | 上升沿中断 |
| 61 | PB9 | ENC_RB | 右编码器B相 | 下降沿中断 |

#### 通信接口系统
| 引脚号 | 引脚名称 | GPIO | 功能描述 | 通信参数 |
|--------|----------|------|----------|----------|
| 21 | PA10 | UART_TX | 串口发送 | 115200bps, 8N1 |
| 22 | PA11 | UART_RX | 串口接收 | 115200bps, 8N1 |
| - | I2C_SDA | - | I2C数据线 | 需配置具体引脚 |
| - | I2C_SCL | - | I2C时钟线 | 需配置具体引脚 |

#### 传感器接口系统
| 引脚号 | 引脚名称 | GPIO | 功能描述 | 配置说明 |
|--------|----------|------|----------|----------|
| 33 | PA0 | GRAY_ADDR0 | 灰度传感器地址位0 | 数字输出 |
| 34 | PA1 | GRAY_ADDR1 | 灰度传感器地址位1 | 数字输出 |
| 35 | PA28 | GRAY_ADDR2 | 灰度传感器地址位2 | 数字输出 |
| 62 | PA27 | ADC_CH0 | 电压采样 | ADC0通道0 |

### 硬件连接示意图

#### 电机驱动连接
```
MCU                    电机驱动器                 电机
┌─────────┐           ┌─────────────┐           ┌─────────┐
│  PB11   ├───PWM────►│    PWM      │           │         │
│  PA22   ├───DIR1───►│    DIR1     ├───────────┤ 左电机  │
│  PB24   ├───DIR2───►│    DIR2     │           │         │
│         │           │             │           └─────────┘
│  PA31   ├───PWM────►│    PWM      │           ┌─────────┐
│  PA24   ├───DIR1───►│    DIR1     ├───────────┤ 右电机  │
│  PA26   ├───DIR2───►│    DIR2     │           │         │
└─────────┘           └─────────────┘           └─────────┘
```

#### 编码器连接
```
MCU                    编码器
┌─────────┐           ┌─────────────┐
│   PB6   ├───────────┤  左编码器A   │
│   PB7   ├───────────┤  左编码器B   │
│   PB8   ├───────────┤  右编码器A   │
│   PB9   ├───────────┤  右编码器B   │
└─────────┘           └─────────────┘
```

#### I2C设备连接
```
MCU                    I2C总线                   设备
┌─────────┐           ┌─────────────┐           ┌─────────────┐
│  SDA    ├───────────┤    SDA      ├───────────┤ 灰度传感器  │
│  SCL    ├───────────┤    SCL      │           │ (0x4C)      │
└─────────┘           │             │           └─────────────┘
                      │             │           ┌─────────────┐
                      │             ├───────────┤ OLED显示屏  │
                      │             │           │ (0x3C)      │
                      └─────────────┘           └─────────────┘
                           │
                      ┌─────────┐
                      │ 4.7kΩ   │ 上拉电阻
                      │ 上拉    │
                      └─────────┘
```

---

## ⚙️ 系统配置详解

### 时钟系统配置

```c
// 系统时钟配置 (来自 ti_msp_dl_config.h)
#define CPUCLK_FREQ                    80000000  // 80MHz主频
#define MOTOR_PWM_LEFT_INST_CLK_FREQ   20000000  // PWM时钟20MHz
#define UART_0_INST_FREQUENCY          4000000   // UART时钟4MHz
```

**时钟树结构**：
```
外部晶振(8MHz) → PLL(×10) → 分频器(÷1) → 系统时钟(80MHz)
                                    ├─→ PWM时钟(20MHz)
                                    ├─→ UART时钟(4MHz)
                                    └─→ ADC时钟(10MHz)
```

### 中断系统配置

| 中断源 | 中断号 | 优先级 | 处理函数 |
|--------|--------|--------|----------|
| GPIOB | GPIOB_INT_IRQn | 高 | GROUP1_IRQHandler |
| UART0 | UART0_INT_IRQn | 中 | UART0_IRQHandler |
| ADC0 | ADC0_INT_IRQn | 中 | ADC0_IRQHandler |
| SysTick | SysTick_IRQn | 低 | SysTick_Handler |

---

## 📁 项目文件结构

```
ti_template/
├── 📁 driver/              # 硬件驱动层
│   ├── 🔧 motor_driver.h/c      # 电机驱动
│   ├── 📊 encoder_driver.h/c    # 编码器驱动  
│   ├── 📡 uart_driver.h/c       # 串口驱动
│   ├── 🔗 IIC.h/c              # I2C底层驱动
│   ├── 🎯 pid.h/c              # PID控制器
│   └── 🏠 bsp_system.h         # 系统头文件
├── 📁 logic/               # 逻辑控制层
│   ├── ⏰ scheduler.h/c         # 任务调度器
│   ├── 👁️ gray_app.h/c         # 灰度传感器应用
│   ├── 🔧 user_motor.h/c       # 电机控制逻辑
│   ├── 🎯 user_pid.h/c         # PID应用逻辑
│   └── 📺 OLED.h/c             # OLED显示控制
├── 📁 user/                # 用户应用层
│   ├── 🏠 main.c               # 主程序
│   └── 🔧 [各模块实现文件]
├── 📁 keil/                # Keil工程文件
├── ⚙️ ti_msp_dl_config.h/c  # 硬件配置
├── ⚙️ empty.syscfg          # SysConfig配置
└── 📖 README.md            # 项目说明
```

---

## 🔄 系统工作流程

### 主程序执行流程

```c
int main(void)
{
    // 1. 系统硬件初始化
    SYSCFG_DL_init();
    
    // 2. 用户配置初始化
    user_config();
    
    // 3. 任务调度器初始化
    scheduler_init();
    
    // 4. 主循环 - 任务调度
    while (1)
    {
        scheduler_run();  // 执行任务调度
    }
}
```

### 任务调度机制

系统采用基于时间片的协作式多任务调度：

```c
// 任务结构体定义
typedef struct{
    void (*task_func)(void);    // 任务函数指针
    uint32_t rate_ms;          // 执行周期(ms)
    uint32_t last_run;         // 上次执行时间
}task_t;

// 任务列表配置
static task_t scheduler_task[] = {
    {uart0_task,     5,  0},   // 串口任务，5ms周期
    {user_gray_task, 1,  0},   // 灰度检测，1ms周期
    // 可根据需要添加更多任务
};
```

---

## 🛠️ 开发环境配置

### 必需软件工具

1. **Keil MDK 5.37+**
   - ARM编译器支持
   - 项目管理和调试
   - 下载地址：[Keil官网](https://www.keil.com/mdk5/)

2. **MSPM0 SDK 2.04.00.06**
   - TI官方驱动库
   - 硬件抽象层支持
   - 下载地址：[TI官网](https://www.ti.com/tool/MSPM0-SDK)

3. **SysConfig工具**
   - 图形化配置工具
   - 自动生成配置代码
   - 集成在SDK中

4. **J-Link调试器**
   - 在线调试支持
   - 程序下载功能
   - 支持SWD接口

### 编译配置说明

**编译器设置**：
```
Target: ARM Cortex-M0+
Compiler: ARM Compiler 6
Optimization: -O1 (平衡优化)
Debug: Full Debug Info
```

**链接器配置**：
```
Scatter File: mspm0g3507.sct
Stack Size: 0x400 (1KB)
Heap Size: 0x200 (512B)
```

---

## 🔧 核心功能模块预览

本项目包含7大核心功能模块，每个模块都有独立的驱动层和应用层实现：

### 1. 电机控制系统 🔧
- **驱动文件**：`motor_driver.h/c`
- **应用文件**：`user_motor.h/c`
- **核心功能**：PWM调速、方向控制、双电机协调

### 2. 编码器反馈系统 📊
- **驱动文件**：`encoder_driver.h/c`
- **核心功能**：正交编码器解码、速度计算、位置反馈

### 3. 灰度传感器系统 👁️
- **应用文件**：`gray_app.h/c`
- **核心功能**：I2C通信、8路灰度检测、线位置计算

### 4. OLED显示系统 📺
- **应用文件**：`OLED.h/c`
- **核心功能**：SSD1306控制、字符显示、中文支持

### 5. PID控制系统 🎯
- **驱动文件**：`pid.h/c`
- **应用文件**：`user_pid.h/c`
- **核心功能**：位置式PID、增量式PID、参数调节

### 6. UART通信系统 📡
- **驱动文件**：`uart_driver.h/c`
- **核心功能**：串口通信、printf重定向、调试输出

### 7. 任务调度系统 ⏰
- **应用文件**：`scheduler.h/c`
- **核心功能**：多任务调度、时间管理、系统节拍

---

## 📋 快速上手指南

### 第一步：环境准备
1. 安装Keil MDK和MSPM0 SDK
2. 连接J-Link调试器到开发板
3. 确认硬件连接正确

### 第二步：编译下载
1. 打开`keil/main.uvprojx`工程文件
2. 选择目标设备：MSPM0G3507
3. 编译项目（F7键）
4. 下载程序（F8键）

### 第三步：功能测试
1. 打开串口调试助手（115200bps）
2. 观察OLED显示内容
3. 测试电机控制功能
4. 验证传感器数据

### 第四步：参数调节
1. 根据实际硬件调整PID参数
2. 校准灰度传感器黑白值
3. 优化电机控制参数

---

## 🔍 调试方法说明

### 串口调试输出
项目支持printf重定向到UART，可以方便地输出调试信息：

```c
// 调试输出示例
my_printf(UART_0_INST, "System Init OK\r\n");
my_printf(UART_0_INST, "ADC Value: %d\r\n", adc_value);
```

### OLED状态显示
通过OLED实时显示系统状态：

```c
// OLED显示示例
OLED_ShowString(0, 0, "Smart Car v1.0");
OLED_ShowNum(0, 16, encoder_left.speed_cm_s, 4);
```

### J-Link在线调试
1. 设置断点进行单步调试
2. 观察变量值变化
3. 分析程序执行流程

---

## ⚠️ 注意事项

### 硬件连接注意事项
1. **电源管理**：确保3.3V稳定供电
2. **I2C上拉**：SDA和SCL需要4.7kΩ上拉电阻
3. **编码器信号**：注意信号线屏蔽，避免干扰
4. **电机驱动**：确认驱动器使能信号连接

### 软件配置注意事项
1. **I2C配置**：需要在SysConfig中配置I2C引脚
2. **中断优先级**：合理设置各中断的优先级
3. **任务周期**：根据实际需求调整任务执行周期
4. **内存管理**：注意栈和堆的大小配置

### 常见问题预防
1. **编译错误**：检查SDK路径配置
2. **下载失败**：确认调试器连接和目标设备选择
3. **运行异常**：检查时钟配置和中断设置
4. **通信故障**：验证波特率和引脚配置

---

*接下来的章节将详细解析每个功能模块的实现原理和代码细节，包括具体的代码示例和调试方法。*
